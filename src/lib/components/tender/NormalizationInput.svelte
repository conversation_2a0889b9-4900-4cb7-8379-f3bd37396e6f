<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import * as Select from '$lib/components/ui/select';
	import { Label } from '$lib/components/ui/label';
	import { formatCurrency } from '$lib/utils';

	interface Props {
		type?: 'amount' | 'percentage';
		amount: string;
		percentage: string;
		subtotal: number;
		currency?: string;
		onTypeChange: (type: 'amount' | 'percentage') => void;
		onAmountChange: (amount: string) => void;
		onPercentageChange: (percentage: string) => void;
	}

	let {
		type = $bindable(),
		amount,
		percentage,
		subtotal,
		currency = '$',
		onTypeChange,
		onAmountChange,
		onPercentageChange,
	}: Props = $props();

	// Real-time calculation display
	const calculatedValue = $derived.by(() => {
		if (type === 'amount') {
			const amountValue = parseFloat(amount) || 0;
			const percentageValue = subtotal > 0 ? (amountValue / subtotal) * 100 : 0;
			return `${percentageValue.toFixed(2)}%`;
		} else {
			const percentageValue = parseFloat(percentage) || 0;
			const amountValue = (subtotal * percentageValue) / 100;
			return formatCurrency(amountValue, {
				symbol: currency,
				fallback: '-',
			});
		}
	});

	const normalizedTotal = $derived.by(() => {
		if (type === 'amount') {
			return subtotal + (parseFloat(amount) || 0);
		} else {
			const percentageValue = parseFloat(percentage) || 0;
			const adjustmentAmount = (subtotal * percentageValue) / 100;
			return subtotal + adjustmentAmount;
		}
	});

	const formattedNormalizedTotal = $derived(
		formatCurrency(normalizedTotal, {
			symbol: currency,
			fallback: '-',
		}),
	);
</script>

<div class="space-y-4 rounded-lg border bg-gray-50 p-4">
	<div class="flex items-center justify-between">
		<Label class="text-sm font-medium">Normalization</Label>
		<div class="text-xs text-gray-500">
			Subtotal: {formatCurrency(subtotal, {
				symbol: currency,
				fallback: '-',
			})}
		</div>
	</div>

	<div class="space-y-3">
		<!-- Type Selection -->
		<div>
			<Label class="text-xs text-gray-500">Input Type</Label>
			<Select.Root
				type="single"
				value={type}
				onValueChange={(value) => {
					if (value) {
						type = value as 'amount' | 'percentage';
						onTypeChange(value as 'amount' | 'percentage');
					}
				}}
			>
				<Select.Trigger class="w-full">
					{type === 'amount' ? 'Fixed Amount' : 'Percentage'}
				</Select.Trigger>
				<Select.Content>
					<Select.Item value="amount">Fixed Amount</Select.Item>
					<Select.Item value="percentage">Percentage</Select.Item>
				</Select.Content>
			</Select.Root>
		</div>

		<!-- Input Fields -->
		<div class="grid grid-cols-2 gap-3">
			<div>
				<Label class="text-xs text-gray-500">
					{type === 'amount' ? `Amount (${currency})` : 'Percentage (%)'}
				</Label>
				{#if type === 'amount'}
					<Input
						type="number"
						step="0.01"
						placeholder="0.00"
						value={amount}
						oninput={(e) => onAmountChange(e.currentTarget.value)}
						class="text-sm"
					/>
				{:else}
					<Input
						type="number"
						step="0.01"
						placeholder="0.00"
						value={percentage}
						oninput={(e) => onPercentageChange(e.currentTarget.value)}
						class="text-sm"
					/>
				{/if}
			</div>

			<div>
				<Label class="text-xs text-gray-500">
					{type === 'amount' ? 'Equivalent %' : 'Equivalent Amount'}
				</Label>
				<div class="rounded-md border bg-white px-3 py-2 text-sm text-gray-600">
					{calculatedValue}
				</div>
			</div>
		</div>

		<!-- Real-time Total Display -->
		<div class="border-t pt-2">
			<div class="flex items-center justify-between">
				<span class="text-sm font-medium">Normalized Total:</span>
				<span class="text-lg font-bold text-green-600">
					{formattedNormalizedTotal}
				</span>
			</div>
			<div class="mt-1 text-xs text-gray-500">
				{type === 'amount'
					? `${formatCurrency(subtotal, { symbol: currency, fallback: '-' })} + ${formatCurrency(parseFloat(amount) || 0, { symbol: currency, fallback: '-' })}`
					: `${formatCurrency(subtotal, { symbol: currency, fallback: '-' })} + ${formatCurrency((subtotal * (parseFloat(percentage) || 0)) / 100, { symbol: currency, fallback: '-' })} (${percentage || 0}%)`}
			</div>
		</div>
	</div>
</div>
